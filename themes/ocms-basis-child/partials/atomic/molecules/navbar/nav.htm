{% set nav = mainmenuItems %}
{% if menu %}
    {% set nav = menu %}
{% endif %}

<div id="main-nav" role="navigation" aria-label="Main">
    <ul>

        {% for item in nav %}

            {% if not item.viewBag.isHidden %}

                {% if item.type == 'header' and item.url != '' %}
                    <li class="relative group/menuitem {{ item.viewBag.cssClass }} {{ item.isActive or item.isChildActive ? 'active' }}">
                        {% partial 'atomic/atoms/navbar/nav-header' %}

                        {% if item.items %}
                            {% partial 'atomic/molecules/navbar/dropdown' %}
                        {% endif %}
                    </li>

                {% elseif  item.url != '' %}
                    <li class="relative group/menuitem {{ item.viewBag.cssClass }} {{ item.isActive or item.isChildActive ? 'active' }}">
                        {% partial 'atomic/atoms/navbar/nav-link' %}

                        {% if item.items %}
                            {% partial 'atomic/molecules/navbar/dropdown' %}
                        {% endif %}
                    </li>
                {% endif %}

            {% endif %}

        {% endfor %}
    </ul>
</div>
