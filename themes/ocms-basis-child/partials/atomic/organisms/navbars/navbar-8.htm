[staticMenu mainMenu]
code = "main-menu"
==
{% set mainmenuItems = mainMenu.resetMenu(navbar.mainmenu) %}

<header data-section="navbar" data-name="navbar-8" class="border-b border-gray-900/10 relative z-[51]" x-data="{ open: false }">
    <div class="px-4 md:px-6 lg:px-8 py-1.5 {{ bgSecondary|default('bg-white') }} border-b border-gray-900/5 hidden xs:block md:hidden">
        <div class="flex justify-between items-center gap-x-8 container">
            {% partial 'atomic/molecules/navbar/contact-links' %}
        </div>
    </div>

    {% partial 'atomic/organisms/navbars/navbar-bg-wrap' body %}
        <div class="flex justify-between items-center gap-y-3 md:gap-y-0 md:justify-normal md:gap-x-8 container">

            <div class="relative shrink-0 flex items-center">
                {% partial 'atomic/atoms/navbar/logo' %}
            </div>

            <div class="ml-auto md:hidden">
                {% partial 'atomic/atoms/navbar/bars' icon="bars-staggered" %}
            </div>

            <div class="md:shrink-0 md:ml-auto hidden md:flex items-center justify-end space-x-8">
                {% partial 'atomic/molecules/navbar/contact-links' %}
            </div>

        </div>
    {% endpartial %}

    <div class="relative hidden xl:block">
        <div class="flex justify-end">
            <div class="w-full relative">
                <div class="absolute inset-y-0 left-1/2 right-0 bg-primary-950 -z-10 rounded-bl-dijkstra"></div>
                <div class="container flex justify-end space-x-6 p-4 text-white">
                    {% partial 'atomic/molecules/navbar/nav' %}
                </div>
            </div>
        </div>
    </div>
    <div class="hidden xl:block relative">
        <div class="">
            <div class="">

            </div>
        </div>
    </div>

    {% partial 'atomic/organisms/navbars/mobile' %}

</header>
